"""
圆锥圆弧体积计算模块

在圆锥之上某个高度生成圆弧，向下延伸至圆锥壁，计算所夹图形的体积。
"""
import math
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from typing import Tuple, List, Optional, Union
from sympy import symbols, integrate, pi as sym_pi, cos, sin

from tools.basics import calTan_h


class ConeArcVolume:
    """圆锥圆弧体积计算类"""
    
    def __init__(self, cone_angle: float, cone_height: float):
        """
        初始化圆锥圆弧体积计算器
        
        Args:
            cone_angle (float): 圆锥半顶角（度）
            cone_height (float): 圆锥高度
        """
        self.cone_angle = cone_angle
        self.cone_height = cone_height
        self.cone_angle_rad = math.radians(cone_angle)
        
    def get_cone_radius_at_height(self, height: float) -> float:
        """
        获取圆锥在指定高度处的半径
        
        Args:
            height (float): 高度
            
        Returns:
            float: 该高度处的圆锥半径
        """
        return height * math.tan(self.cone_angle_rad)
    
    def generate_arc_at_height(self, height: float, arc_radius: float, 
                              arc_center_x: float = 0, arc_center_y: float = 0,
                              start_angle: float = 0, end_angle: float = 180) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        在指定高度生成圆弧，只保留圆锥内部的部分
        
        Args:
            height (float): 圆弧所在高度
            arc_radius (float): 圆弧半径
            arc_center_x (float): 圆弧中心x坐标
            arc_center_y (float): 圆弧中心y坐标
            start_angle (float): 起始角度（度）
            end_angle (float): 结束角度（度）
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: 圆弧的x, y, z坐标数组
        """
        cone_radius = self.get_cone_radius_at_height(height)
        
        # 生成圆弧角度范围
        angles = np.linspace(math.radians(start_angle), math.radians(end_angle), 100)
        
        # 计算圆弧上的点
        x_arc = arc_center_x + arc_radius * np.cos(angles)
        y_arc = arc_center_y + arc_radius * np.sin(angles)
        z_arc = np.full_like(x_arc, height)
        
        # 过滤出圆锥内部的点
        distances_from_origin = np.sqrt(x_arc**2 + y_arc**2)
        inside_cone = distances_from_origin <= cone_radius
        
        return x_arc[inside_cone], y_arc[inside_cone], z_arc[inside_cone]
    
    def extend_arc_to_cone_wall(self, x_arc: np.ndarray, y_arc: np.ndarray, 
                               z_arc: np.ndarray) -> Tuple[List[np.ndarray], List[np.ndarray], List[np.ndarray]]:
        """
        将圆弧上的每个点竖直向下延伸到圆锥壁
        
        Args:
            x_arc (np.ndarray): 圆弧x坐标
            y_arc (np.ndarray): 圆弧y坐标
            z_arc (np.ndarray): 圆弧z坐标
            
        Returns:
            Tuple[List[np.ndarray], List[np.ndarray], List[np.ndarray]]: 延伸线段的坐标列表
        """
        extended_lines_x = []
        extended_lines_y = []
        extended_lines_z = []
        
        for i in range(len(x_arc)):
            x_point = x_arc[i]
            y_point = y_arc[i]
            z_start = z_arc[i]
            
            # 计算该点到圆锥壁的距离
            distance_from_origin = math.sqrt(x_point**2 + y_point**2)
            
            # 计算与圆锥壁相交的高度
            # 圆锥方程: r = h * tan(angle)
            # 对于点(x_point, y_point)，相交高度为: h = r / tan(angle)
            z_intersection = distance_from_origin / math.tan(self.cone_angle_rad)
            
            # 生成从圆弧点到圆锥壁的竖直线段
            z_line = np.linspace(z_start, z_intersection, 50)
            x_line = np.full_like(z_line, x_point)
            y_line = np.full_like(z_line, y_point)
            
            extended_lines_x.append(x_line)
            extended_lines_y.append(y_line)
            extended_lines_z.append(z_line)
            
        return extended_lines_x, extended_lines_y, extended_lines_z
    
    def calculate_volume_between_arc_and_cone(self, height: float, arc_radius: float,
                                            arc_center_x: float = 0, arc_center_y: float = 0,
                                            start_angle: float = 0, end_angle: float = 180) -> float:
        """
        计算圆弧延伸形成的图形与圆锥壁之间的体积
        
        Args:
            height (float): 圆弧所在高度
            arc_radius (float): 圆弧半径
            arc_center_x (float): 圆弧中心x坐标
            arc_center_y (float): 圆弧中心y坐标
            start_angle (float): 起始角度（度）
            end_angle (float): 结束角度（度）
            
        Returns:
            float: 计算得到的体积
        """
        # 使用符号积分计算体积
        theta, z = symbols('theta z', real=True)
        
        # 将角度转换为弧度
        start_rad = math.radians(start_angle)
        end_rad = math.radians(end_angle)
        
        # 圆弧参数方程
        x_arc_sym = arc_center_x + arc_radius * cos(theta)
        y_arc_sym = arc_center_y + arc_radius * sin(theta)
        
        # 计算圆弧上点到原点的距离
        r_arc = (x_arc_sym**2 + y_arc_sym**2)**0.5
        
        # 圆锥在高度z处的半径
        r_cone = z * math.tan(self.cone_angle_rad)
        
        # 只考虑圆弧在圆锥内部的部分
        # 对于每个角度theta，计算从圆弧高度到圆锥壁的积分
        
        # 简化计算：使用数值方法
        return self._calculate_volume_numerical(height, arc_radius, arc_center_x, 
                                              arc_center_y, start_angle, end_angle)
    
    def _calculate_volume_numerical(self, height: float, arc_radius: float,
                                  arc_center_x: float, arc_center_y: float,
                                  start_angle: float, end_angle: float) -> float:
        """
        使用数值方法计算体积
        
        Args:
            height (float): 圆弧所在高度
            arc_radius (float): 圆弧半径
            arc_center_x (float): 圆弧中心x坐标
            arc_center_y (float): 圆弧中心y坐标
            start_angle (float): 起始角度（度）
            end_angle (float): 结束角度（度）
            
        Returns:
            float: 计算得到的体积
        """
        # 生成圆弧上的采样点
        x_arc, y_arc, z_arc = self.generate_arc_at_height(
            height, arc_radius, arc_center_x, arc_center_y, start_angle, end_angle
        )
        
        if len(x_arc) == 0:
            return 0.0
        
        total_volume = 0.0
        
        # 对每个圆弧段计算体积
        for i in range(len(x_arc) - 1):
            # 当前段的两个端点
            x1, y1 = x_arc[i], y_arc[i]
            x2, y2 = x_arc[i + 1], y_arc[i + 1]
            
            # 计算段长度
            segment_length = math.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            
            # 计算该段中点到原点的距离
            x_mid = (x1 + x2) / 2
            y_mid = (y1 + y2) / 2
            r_mid = math.sqrt(x_mid**2 + y_mid**2)
            
            # 计算该点与圆锥壁相交的高度
            z_intersection = r_mid / math.tan(self.cone_angle_rad)
            
            # 计算该段的体积贡献（近似为梯形柱体）
            if z_intersection < height:
                height_diff = height - z_intersection
                # 使用简单的柱体近似
                segment_volume = segment_length * height_diff * (arc_radius / 100)  # 近似厚度
                total_volume += segment_volume
        
        return total_volume
    
    def visualize_cone_arc_volume(self, height: float, arc_radius: float,
                                 arc_center_x: float = 0, arc_center_y: float = 0,
                                 start_angle: float = 0, end_angle: float = 180,
                                 show_cone: bool = True, show_arc: bool = True,
                                 show_extensions: bool = True) -> None:
        """
        可视化圆锥、圆弧和延伸图形
        
        Args:
            height (float): 圆弧所在高度
            arc_radius (float): 圆弧半径
            arc_center_x (float): 圆弧中心x坐标
            arc_center_y (float): 圆弧中心y坐标
            start_angle (float): 起始角度（度）
            end_angle (float): 结束角度（度）
            show_cone (bool): 是否显示圆锥
            show_arc (bool): 是否显示圆弧
            show_extensions (bool): 是否显示延伸线
        """
        fig = plt.figure(figsize=(12, 10))
        ax = fig.add_subplot(111, projection='3d')
        
        # 绘制圆锥
        if show_cone:
            self._draw_cone(ax)
        
        # 生成并绘制圆弧
        if show_arc:
            x_arc, y_arc, z_arc = self.generate_arc_at_height(
                height, arc_radius, arc_center_x, arc_center_y, start_angle, end_angle
            )
            if len(x_arc) > 0:
                ax.plot(x_arc, y_arc, z_arc, 'r-', linewidth=3, label='圆弧')
        
        # 绘制延伸线
        if show_extensions and show_arc:
            extended_x, extended_y, extended_z = self.extend_arc_to_cone_wall(x_arc, y_arc, z_arc)
            for i in range(0, len(extended_x), 5):  # 每5条线显示一条，避免过于密集
                ax.plot(extended_x[i], extended_y[i], extended_z[i], 'b-', alpha=0.6, linewidth=1)
        
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z (高度)')
        ax.set_title(f'圆锥圆弧体积计算可视化\n圆锥角度: {self.cone_angle}°, 圆弧高度: {height}')
        ax.legend()
        
        plt.tight_layout()
        plt.show()
    
    def _draw_cone(self, ax):
        """绘制圆锥"""
        # 生成圆锥表面
        z_cone = np.linspace(0, self.cone_height, 50)
        theta_cone = np.linspace(0, 2 * np.pi, 50)
        Z_cone, Theta_cone = np.meshgrid(z_cone, theta_cone)
        
        R_cone = Z_cone * math.tan(self.cone_angle_rad)
        X_cone = R_cone * np.cos(Theta_cone)
        Y_cone = R_cone * np.sin(Theta_cone)
        
        ax.plot_surface(X_cone, Y_cone, Z_cone, alpha=0.3, color='yellow', label='圆锥')


def calculate_cone_arc_volume(cone_angle: float, cone_height: float, arc_height: float,
                            arc_radius: float, arc_center_x: float = 0, arc_center_y: float = 0,
                            start_angle: float = 0, end_angle: float = 180,
                            visualize: bool = False) -> float:
    """
    计算圆锥圆弧体积的便捷函数
    
    Args:
        cone_angle (float): 圆锥半顶角（度）
        cone_height (float): 圆锥高度
        arc_height (float): 圆弧所在高度
        arc_radius (float): 圆弧半径
        arc_center_x (float): 圆弧中心x坐标
        arc_center_y (float): 圆弧中心y坐标
        start_angle (float): 起始角度（度）
        end_angle (float): 结束角度（度）
        visualize (bool): 是否显示可视化
        
    Returns:
        float: 计算得到的体积
    """
    calculator = ConeArcVolume(cone_angle, cone_height)
    
    if visualize:
        calculator.visualize_cone_arc_volume(
            arc_height, arc_radius, arc_center_x, arc_center_y, start_angle, end_angle
        )
    
    volume = calculator.calculate_volume_between_arc_and_cone(
        arc_height, arc_radius, arc_center_x, arc_center_y, start_angle, end_angle
    )
    
    return volume


if __name__ == '__main__':
    # 测试示例
    cone_angle = 5  # 圆锥半顶角5度
    cone_height = 1000  # 圆锥高度1000单位
    arc_height = 800  # 圆弧在高度800处
    arc_radius = 50  # 圆弧半径50单位
    
    print(f"圆锥参数: 半顶角={cone_angle}°, 高度={cone_height}")
    print(f"圆弧参数: 高度={arc_height}, 半径={arc_radius}")
    
    # 计算体积
    volume = calculate_cone_arc_volume(
        cone_angle=cone_angle,
        cone_height=cone_height,
        arc_height=arc_height,
        arc_radius=arc_radius,
        start_angle=0,
        end_angle=180,
        visualize=True
    )
    
    print(f"计算得到的体积: {volume:.2f}")
