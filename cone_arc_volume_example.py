"""
圆锥圆弧体积计算示例

演示如何使用圆锥圆弧体积计算模块。
功能：在圆锥之上某个高度生成圆弧，向下延伸至圆锥壁，计算所夹图形的体积。
"""
import matplotlib
matplotlib.use('Qt5Agg')  # 使用Qt5Agg后端

from tools.cone_arc_volume import calculate_cone_arc_volume, ConeArcVolume
from tools.environment import show_cone_from_yaml


def simple_example():
    """简单示例：基本的圆锥圆弧体积计算"""
    print("=" * 50)
    print("简单示例：基本圆锥圆弧体积计算")
    print("=" * 50)
    
    # 定义圆锥参数
    cone_angle = 5      # 圆锥半顶角5度
    cone_height = 1000  # 圆锥高度1000单位
    
    # 定义圆弧参数
    arc_height = 800    # 圆弧在高度800处
    arc_radius = 50     # 圆弧半径50单位
    start_angle = 0     # 起始角度0度
    end_angle = 180     # 结束角度180度（半圆弧）
    
    print(f"圆锥参数：半顶角={cone_angle}°，高度={cone_height}")
    print(f"圆弧参数：高度={arc_height}，半径={arc_radius}，角度范围={start_angle}°-{end_angle}°")
    
    # 计算体积
    volume = calculate_cone_arc_volume(
        cone_angle=cone_angle,
        cone_height=cone_height,
        arc_height=arc_height,
        arc_radius=arc_radius,
        start_angle=start_angle,
        end_angle=end_angle,
        visualize=False  # 不显示可视化
    )
    
    print(f"计算结果：圆弧延伸图形与圆锥壁之间的体积 = {volume:.2f} 立方单位")
    print()


def fantasy_project_example():
    """Fantasy项目示例：使用项目中的实际圆锥数据"""
    print("=" * 50)
    print("Fantasy项目示例：使用实际圆锥数据")
    print("=" * 50)
    
    try:
        # 使用Fantasy项目的圆锥数据
        perimeter = 360000  # 标准位置周长（km）
        h = 0              # 高度差
        
        # 获取圆锥数据
        cone, details = show_cone_from_yaml(perimeter, h, if_print=False)
        
        print(f"Fantasy项目圆锥数据：")
        print(f"  半顶角：{details['angle']}°")
        print(f"  标准高度：{cone.standard_height:.0f} km")
        print(f"  标准半径：{cone.standard_radius:.0f} km")
        
        # 设置圆弧参数（相对较小的圆弧）
        arc_height = cone.standard_height + 20000  # 圆弧在标准高度上方20km处
        arc_radius = 5000                          # 圆弧半径5000km
        
        print(f"\n圆弧参数：")
        print(f"  圆弧高度：{arc_height:.0f} km")
        print(f"  圆弧半径：{arc_radius:.0f} km")
        
        # 计算不同角度范围的体积
        test_cases = [
            (0, 90, "四分之一圆弧"),
            (0, 180, "半圆弧"),
            (0, 360, "完整圆弧")
        ]
        
        print(f"\n体积计算结果：")
        for start_angle, end_angle, description in test_cases:
            volume = calculate_cone_arc_volume(
                cone_angle=details['angle'],
                cone_height=cone.standard_height + 50000,  # 圆锥总高度
                arc_height=arc_height,
                arc_radius=arc_radius,
                start_angle=start_angle,
                end_angle=end_angle,
                visualize=False
            )
            print(f"  {description}：{volume:.2e} km³")
        
    except Exception as e:
        print(f"读取Fantasy项目数据时出错：{e}")
        print("请确保data/Cone.yaml文件存在且格式正确")
    
    print()


def advanced_example():
    """高级示例：使用ConeArcVolume类进行详细计算"""
    print("=" * 50)
    print("高级示例：详细的圆锥圆弧分析")
    print("=" * 50)
    
    # 创建圆锥圆弧计算器
    cone_angle = 5
    cone_height = 1000
    calculator = ConeArcVolume(cone_angle, cone_height)
    
    print(f"圆锥参数：半顶角={cone_angle}°，高度={cone_height}")
    
    # 分析不同高度处的圆锥半径
    heights = [600, 700, 800, 900]
    print(f"\n不同高度处的圆锥半径：")
    for h in heights:
        radius = calculator.get_cone_radius_at_height(h)
        print(f"  高度 {h}：半径 {radius:.2f}")
    
    # 分析圆弧生成
    arc_height = 800
    arc_radius = 50
    print(f"\n圆弧分析（高度={arc_height}，半径={arc_radius}）：")
    
    x_arc, y_arc, z_arc = calculator.generate_arc_at_height(
        arc_height, arc_radius, 0, 0, 0, 180
    )
    print(f"  生成的圆弧点数：{len(x_arc)}")
    
    if len(x_arc) > 0:
        print(f"  圆弧范围：x=[{min(x_arc):.2f}, {max(x_arc):.2f}], y=[{min(y_arc):.2f}, {max(y_arc):.2f}]")
        
        # 分析延伸线
        extended_x, extended_y, extended_z = calculator.extend_arc_to_cone_wall(x_arc, y_arc, z_arc)
        print(f"  延伸线数量：{len(extended_x)}")
        
        if len(extended_z) > 0 and len(extended_z[0]) > 0:
            min_z = min(min(line) for line in extended_z if len(line) > 0)
            max_z = max(max(line) for line in extended_z if len(line) > 0)
            print(f"  延伸高度范围：[{min_z:.2f}, {max_z:.2f}]")
    
    # 计算体积
    volume = calculator.calculate_volume_between_arc_and_cone(
        arc_height, arc_radius, 0, 0, 0, 180
    )
    print(f"\n最终体积：{volume:.2f} 立方单位")
    print()


def comparison_example():
    """对比示例：不同参数对体积的影响"""
    print("=" * 50)
    print("对比示例：参数对体积的影响")
    print("=" * 50)
    
    base_params = {
        'cone_angle': 5,
        'cone_height': 1000,
        'arc_height': 800,
        'arc_radius': 50,
        'start_angle': 0,
        'end_angle': 180
    }
    
    print("基准参数：", base_params)
    base_volume = calculate_cone_arc_volume(**base_params, visualize=False)
    print(f"基准体积：{base_volume:.2f}")
    print()
    
    # 测试不同圆弧半径的影响
    print("圆弧半径对体积的影响：")
    for radius in [30, 40, 50, 60, 70]:
        params = base_params.copy()
        params['arc_radius'] = radius
        volume = calculate_cone_arc_volume(**params, visualize=False)
        ratio = volume / base_volume if base_volume > 0 else 0
        print(f"  半径 {radius}：体积 {volume:.2f}（相对比例 {ratio:.2f}）")
    print()
    
    # 测试不同圆弧高度的影响
    print("圆弧高度对体积的影响：")
    for height in [700, 750, 800, 850, 900]:
        params = base_params.copy()
        params['arc_height'] = height
        volume = calculate_cone_arc_volume(**params, visualize=False)
        ratio = volume / base_volume if base_volume > 0 else 0
        print(f"  高度 {height}：体积 {volume:.2f}（相对比例 {ratio:.2f}）")
    print()
    
    # 测试不同角度范围的影响
    print("角度范围对体积的影响：")
    angle_ranges = [(0, 90), (0, 180), (0, 270), (0, 360)]
    for start, end in angle_ranges:
        params = base_params.copy()
        params['start_angle'] = start
        params['end_angle'] = end
        volume = calculate_cone_arc_volume(**params, visualize=False)
        ratio = volume / base_volume if base_volume > 0 else 0
        print(f"  角度 {start}°-{end}°：体积 {volume:.2f}（相对比例 {ratio:.2f}）")
    print()


def main():
    """主函数：运行所有示例"""
    print("圆锥圆弧体积计算模块使用示例")
    print("功能：在圆锥之上某个高度生成圆弧，向下延伸至圆锥壁，计算所夹图形的体积")
    print()
    
    # 运行各种示例
    simple_example()
    fantasy_project_example()
    advanced_example()
    comparison_example()
    
    print("=" * 50)
    print("所有示例运行完成！")
    print("=" * 50)
    print()
    print("使用说明：")
    print("1. 使用 calculate_cone_arc_volume() 函数进行快速计算")
    print("2. 使用 ConeArcVolume 类进行详细分析")
    print("3. 设置 visualize=True 可以显示3D可视化图形")
    print("4. 可以调整圆弧的位置、大小和角度范围")


if __name__ == '__main__':
    main()
