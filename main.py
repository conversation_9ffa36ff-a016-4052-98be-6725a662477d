import matplotlib
import numpy as np

from forecast.CloudOcclusion import cal_cloud_probability, cal_cloud_probability_auto, \
    draw_cone_section_and_random_cylinders_auto, show_cloud_distribution
from tools.environment import show_cone_from_yaml
from tools.cone_arc_volume import calculate_cone_arc_volume, ConeArcVolume


def cloud_print_dots(angle1, angle2, score1, score2, perimeter, h):
    """
    打印云与阳光相交的概率时为确定范围而测试的云与线。

    参数：
    angle1 (float)：角度1（测量点角度，一般不变）。
    angle2 (float)：角度2（太阳角度）。
    score1 (int)：云生成的范围下限。
    score2 (int)：云生成的范围上限。
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 计算圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h)
    cal_cloud_probability(cone.standard_height, angle1, 500000000000, angle2, details['half_nature_height'], cone.standard_radius, details['r_diff'], 3, details['ratios'], details['angle'], score1, score2, "print_dots")


def cloud_probability(angle1, angle2, score1, score2, perimeter, h):
    """
    计算云与阳光相交的概率。

    参数：
    angle1 (float)：角度1（测量点角度，一般不变）。
    angle2 (float)：角度2（太阳角度）。
    score1 (int)：云生成的范围下限。
    score2 (int)：云生成的范围上限。
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 计算圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h)
    cal_cloud_probability(cone.standard_height, angle1, 500000000000, angle2, details['half_nature_height'], cone.standard_radius, details['r_diff'], 3, details['ratios'], details['angle'], score1, score2, "probability")


def cloud_probability_5(angle1, angle2, score1, score2, perimeter, h):
    """
    计算在一天的运动中云与阳光相交的概率。

    参数：
    angle1 (float)：角度1（测量点角度，一般不变）。
    angle2 (float)：角度2（太阳角度）。
    score1 (int)：云生成的范围下限。
    score2 (int)：云生成的范围上限。
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 计算圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h)
    cal_cloud_probability(cone.standard_height, angle1, 500000000000, angle2, details['half_nature_height'], cone.standard_radius, details['r_diff'], 3, details['ratios'], details['angle'], score1, score2, "probability_5")


def cloud_probability_auto(angle1, angle2, perimeter, h):
    """
    计算云与阳光相交的概率。

    参数：
    angle1 (float)：角度1（测量点角度，一般不变）。
    angle2 (float)：角度2（太阳角度）。
    score1 (int)：云生成的范围下限。
    score2 (int)：云生成的范围上限。
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 计算圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h)
    cal_cloud_probability_auto(cone.standard_height, angle1, 500000000000, angle2, details['half_nature_height'], cone.standard_radius, details['r_diff'], 3, details['ratios'], details['angle'])


def draw_cone_section_and_random_cylinders(angle1, angle2, score1, score2, perimeter, h):
    """
    绘制云与阳光相交的概率时为确定范围而测试的云与线。

    参数：
    angle1 (float)：角度1（测量点角度，一般不变）。
    angle2 (float)：角度2（太阳角度）。
    score1 (int)：云生成的范围下限。
    score2 (int)：云生成的范围上限。
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 计算圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h)
    cal_cloud_probability(cone.standard_height, angle1, 500000000000, angle2, details['half_nature_height'], cone.standard_radius, details['r_diff'], 3, details['ratios'], details['angle'], score1, score2, "draw_cone_section_and_random_cylinders")


def draw_cone_section_and_random_cylinders_Isauto(angle1, angle2, perimeter, h):
    """
    绘制云与阳光相交的概率。

    参数：
    angle1 (float)：角度1（测量点角度，一般不变）。
    angle2 (float)：角度2（太阳角度）。
    score1 (int)：云生成的范围下限。
    score2 (int)：云生成的范围上限。
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 计算圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h)
    draw_cone_section_and_random_cylinders_auto(cone.standard_height, angle1, 500000000000, angle2, details['half_nature_height'], cone.standard_radius, details['r_diff'], 3, details['ratios'], details['angle'])


def cloud_probability_auto_all(angle1, perimeter, h):
    """
    计算云与阳光相交的概率。

    参数：
    angle1 (float)：角度1（测量点角度，一般不变）。
    angle2 (float)：角度2（太阳角度）。
    score1 (int)：云生成的范围下限。
    score2 (int)：云生成的范围上限。
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 计算圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h)
    if h != 0:
        for angle2 in range(0, 180, 30):
            cal_cloud_probability_auto(cone.diff_height, angle1, 500000000000, angle2,
                                       details['half_nature_height'] - h,
                                       cone.diff_radius, details['r_diff'], 3, details['ratios'], details['angle'], cone.nature_height_bottom, details['perimeter'])
    else:
        for angle2 in range(0, 180, 30):
            cal_cloud_probability_auto(cone.standard_height, angle1, 500000000000, angle2, details['half_nature_height'],
                                       cone.standard_radius, details['r_diff'], 3, details['ratios'], details['angle'], cone.nature_height_bottom, details['perimeter'])


def cone_arc_volume_demo(perimeter, h):
    """
    圆锥圆弧体积计算演示函数。

    在圆锥之上某个高度生成圆弧，向下延伸至圆锥壁，计算所夹图形的体积。

    参数：
    perimeter (float)：标准位置周长。
    h (float)：给出的高度位置，基于标准位置的差值，存在正负。

    返回：
    None
    """
    # 获取圆锥数据
    cone, details = show_cone_from_yaml(perimeter, h, if_print=False)

    # 设置圆弧参数
    arc_height = cone.standard_height + 50000  # 圆弧在标准高度上方50km处
    arc_radius = cone.standard_radius * 2

    print(f"圆锥参数:")
    print(f"  半顶角: {details['angle']}°")
    print(f"  标准高度: {cone.standard_height:.0f} km")
    print(f"  标准半径: {cone.standard_radius:.0f} km")
    print(f"\n圆弧参数:")
    print(f"  圆弧高度: {arc_height:.0f} km")
    print(f"  圆弧半径: {arc_radius:.0f} km")

    # 计算体积
    volume = calculate_cone_arc_volume(
        cone_angle=details['angle'],
        cone_height=cone.standard_height + 100000,  # 圆锥总高度
        arc_height=arc_height,
        arc_radius=arc_radius,
        start_angle=0,
        end_angle=180,
        visualize=True
    )

    print(f"\n计算结果:")
    print(f"  圆弧延伸图形与圆锥壁之间的体积: {volume:.2f} km³")


if __name__ == '__main__':
    matplotlib.use('Qt5Agg')
    # print(f"高度为10km时，宽度为: {calTan_h(10)} km")
    # print(f"高度为100km时，宽度为: {calTan_h(100)} km")
    # print(f"高度为500km时，宽度为: {calTan_h(500)} km")
    # print(f"高度为1000km时，宽度为: {calTan_h(1000)} km")
    # print(f"高度为5000km时，宽度为: {calTan_h(5000)} km")
    # print(f"高度为10000km时，宽度为: {calTan_h(10000)} km")
    # print(f"高度为50000km时，宽度为: {calTan_h(50000)} km")
    # print(f"高度为100000km时，宽度为: {calTan_h(100000)} km")
    # cal_cloud_probability(654884, 0, 500000000000, 0)

    # cloud_probability(0, 0, -np.pi / 180 * 2.5, np.pi / 180 * 2.5, 360000, -50000)
    # draw_cone_section_and_random_cylinders(0, 0, -np.pi / 180 * 2.5, np.pi / 180 * 2.5, 360000, -50000)
    # cloud_probability_5(0, 0, -np.pi / 180 * 2.5, np.pi / 180 * 2.5, 360000, -50000)
    # cloud_print_dots(0, 0, -np.pi / 180 * 2.5, np.pi / 180 * 2.5, 360000, 33580)
    # cloud_probability_auto(0, 0, 360000, -50000)
    # draw_cone_section_and_random_cylinders_Isauto(0, 150, 360000, -50000)
    # cloud_probability_auto_all(0, 360000, 33580)
    # show_cloud_distribution(3, [100, 100, 100])

    # 圆锥圆弧体积计算演示
    cone_arc_volume_demo(360000, 0)


