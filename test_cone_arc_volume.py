"""
圆锥圆弧体积计算模块测试脚本

测试在圆锥之上某个高度生成圆弧，向下延伸至圆锥壁，计算所夹图形的体积功能。
"""
import matplotlib
matplotlib.use('Qt5Agg')  # 使用Qt5Agg后端

from tools.cone_arc_volume import calculate_cone_arc_volume, ConeArcVolume
from tools.environment import show_cone_from_yaml


def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("圆锥圆弧体积计算模块测试")
    print("=" * 60)
    
    # 测试参数
    cone_angle = 5  # 圆锥半顶角5度
    cone_height = 1000  # 圆锥高度1000单位
    arc_height = 800  # 圆弧在高度800处
    arc_radius = 50  # 圆弧半径50单位
    
    print(f"测试参数:")
    print(f"  圆锥半顶角: {cone_angle}°")
    print(f"  圆锥高度: {cone_height}")
    print(f"  圆弧高度: {arc_height}")
    print(f"  圆弧半径: {arc_radius}")
    print()
    
    # 创建计算器实例
    calculator = ConeArcVolume(cone_angle, cone_height)
    
    # 测试圆锥半径计算
    cone_radius_at_arc = calculator.get_cone_radius_at_height(arc_height)
    print(f"圆锥在圆弧高度({arc_height})处的半径: {cone_radius_at_arc:.2f}")
    
    # 测试圆弧生成
    x_arc, y_arc, z_arc = calculator.generate_arc_at_height(
        arc_height, arc_radius, 0, 0, 0, 180
    )
    print(f"生成的圆弧点数: {len(x_arc)}")
    
    # 测试体积计算
    volume = calculator.calculate_volume_between_arc_and_cone(
        arc_height, arc_radius, 0, 0, 0, 180
    )
    print(f"计算得到的体积: {volume:.2f}")
    print()


def test_different_parameters():
    """测试不同参数下的体积计算"""
    print("=" * 60)
    print("不同参数测试")
    print("=" * 60)
    
    cone_angle = 5
    cone_height = 1000
    
    test_cases = [
        {"arc_height": 800, "arc_radius": 30, "start_angle": 0, "end_angle": 180, "desc": "半圆弧，小半径"},
        {"arc_height": 800, "arc_radius": 50, "start_angle": 0, "end_angle": 180, "desc": "半圆弧，中等半径"},
        {"arc_height": 800, "arc_radius": 70, "start_angle": 0, "end_angle": 180, "desc": "半圆弧，大半径"},
        {"arc_height": 800, "arc_radius": 50, "start_angle": 0, "end_angle": 90, "desc": "四分之一圆弧"},
        {"arc_height": 800, "arc_radius": 50, "start_angle": 0, "end_angle": 360, "desc": "完整圆弧"},
        {"arc_height": 900, "arc_radius": 50, "start_angle": 0, "end_angle": 180, "desc": "更高位置的半圆弧"},
    ]
    
    for i, case in enumerate(test_cases, 1):
        volume = calculate_cone_arc_volume(
            cone_angle=cone_angle,
            cone_height=cone_height,
            arc_height=case["arc_height"],
            arc_radius=case["arc_radius"],
            start_angle=case["start_angle"],
            end_angle=case["end_angle"],
            visualize=False
        )
        print(f"测试 {i}: {case['desc']}")
        print(f"  参数: 高度={case['arc_height']}, 半径={case['arc_radius']}, "
              f"角度={case['start_angle']}°-{case['end_angle']}°")
        print(f"  体积: {volume:.2f}")
        print()


def test_with_fantasy_project_data():
    """使用Fantasy项目的实际数据进行测试"""
    print("=" * 60)
    print("使用Fantasy项目数据测试")
    print("=" * 60)
    
    # 使用项目中的圆锥数据
    perimeter = 360000  # 标准位置周长
    h = 0  # 高度差
    
    try:
        # 获取圆锥数据
        cone, details = show_cone_from_yaml(perimeter, h, if_print=False)
        
        print(f"Fantasy项目圆锥参数:")
        print(f"  半顶角: {details['angle']}°")
        print(f"  标准高度: {cone.standard_height:.0f} km")
        print(f"  标准半径: {cone.standard_radius:.0f} km")
        print(f"  人间界高度: {details['half_nature_height']} km")
        print()
        
        # 设置圆弧参数
        arc_height = cone.standard_height + 50000  # 圆弧在标准高度上方50km处
        arc_radius = cone.standard_radius * 0.2    # 圆弧半径为标准半径的20%
        
        print(f"圆弧参数:")
        print(f"  圆弧高度: {arc_height:.0f} km")
        print(f"  圆弧半径: {arc_radius:.0f} km")
        print()
        
        # 计算不同角度范围的体积
        angle_ranges = [
            (0, 90, "四分之一圆弧"),
            (0, 180, "半圆弧"),
            (0, 270, "四分之三圆弧"),
            (0, 360, "完整圆弧")
        ]
        
        for start_angle, end_angle, desc in angle_ranges:
            volume = calculate_cone_arc_volume(
                cone_angle=details['angle'],
                cone_height=cone.standard_height + 100000,
                arc_height=arc_height,
                arc_radius=arc_radius,
                start_angle=start_angle,
                end_angle=end_angle,
                visualize=False
            )
            print(f"{desc}: {volume:.2f} km³")
        
        print()
        
    except Exception as e:
        print(f"使用Fantasy项目数据时出错: {e}")
        print("将使用默认参数进行测试")
        
        # 使用默认参数
        volume = calculate_cone_arc_volume(
            cone_angle=5,
            cone_height=700000,
            arc_height=600000,
            arc_radius=30000,
            start_angle=0,
            end_angle=180,
            visualize=False
        )
        print(f"默认参数测试体积: {volume:.2f} km³")


def test_visualization():
    """测试可视化功能"""
    print("=" * 60)
    print("可视化测试")
    print("=" * 60)
    
    print("正在生成可视化图形...")
    
    # 使用适中的参数进行可视化
    volume = calculate_cone_arc_volume(
        cone_angle=5,
        cone_height=1000,
        arc_height=800,
        arc_radius=50,
        start_angle=0,
        end_angle=180,
        visualize=True
    )
    
    print(f"可视化测试完成，计算体积: {volume:.2f}")


def main():
    """主测试函数"""
    print("开始圆锥圆弧体积计算模块测试...")
    print()
    
    # 基本功能测试
    test_basic_functionality()
    
    # 不同参数测试
    test_different_parameters()
    
    # 使用Fantasy项目数据测试
    test_with_fantasy_project_data()
    
    # 可视化测试
    try:
        test_visualization()
    except Exception as e:
        print(f"可视化测试失败: {e}")
        print("这可能是由于显示环境问题，功能本身应该正常")
    
    print("=" * 60)
    print("所有测试完成！")
    print("=" * 60)


if __name__ == '__main__':
    main()
