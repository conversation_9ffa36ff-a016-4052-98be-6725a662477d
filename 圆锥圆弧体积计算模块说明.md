# 圆锥圆弧体积计算模块

## 功能概述

本模块实现了在圆锥之上某个高度生成圆弧，让圆弧竖直向下延伸直到与圆锥壁相交，然后计算这个图形与圆锥壁之间所夹图形的体积。

## 主要特性

- **圆弧生成**：在指定高度生成与圆锥相交的圆弧，只保留圆锥内部的部分
- **延伸计算**：将圆弧上的每个点竖直向下延伸到圆锥壁
- **体积计算**：使用数值积分方法计算延伸图形与圆锥壁之间的体积
- **可视化**：提供3D可视化功能，直观显示圆锥、圆弧和延伸图形
- **灵活参数**：支持自定义圆弧位置、大小、角度范围等参数

## 文件结构

```
tools/cone_arc_volume.py          # 主要功能模块
test_cone_arc_volume.py           # 测试脚本
cone_arc_volume_example.py        # 使用示例
圆锥圆弧体积计算模块说明.md        # 本文档
```

## 核心类和函数

### ConeArcVolume 类

主要的圆锥圆弧体积计算类，包含以下方法：

- `__init__(cone_angle, cone_height)`: 初始化圆锥参数
- `get_cone_radius_at_height(height)`: 获取圆锥在指定高度处的半径
- `generate_arc_at_height(...)`: 在指定高度生成圆弧
- `extend_arc_to_cone_wall(...)`: 将圆弧延伸到圆锥壁
- `calculate_volume_between_arc_and_cone(...)`: 计算体积
- `visualize_cone_arc_volume(...)`: 可视化显示

### 便捷函数

- `calculate_cone_arc_volume(...)`: 一站式体积计算函数

## 使用方法

### 基本使用

```python
from tools.cone_arc_volume import calculate_cone_arc_volume

# 计算体积
volume = calculate_cone_arc_volume(
    cone_angle=5,        # 圆锥半顶角（度）
    cone_height=1000,    # 圆锥高度
    arc_height=800,      # 圆弧所在高度
    arc_radius=50,       # 圆弧半径
    start_angle=0,       # 起始角度（度）
    end_angle=180,       # 结束角度（度）
    visualize=True       # 是否显示可视化
)

print(f"计算得到的体积: {volume:.2f}")
```

### 高级使用

```python
from tools.cone_arc_volume import ConeArcVolume

# 创建计算器实例
calculator = ConeArcVolume(cone_angle=5, cone_height=1000)

# 生成圆弧
x_arc, y_arc, z_arc = calculator.generate_arc_at_height(
    height=800, arc_radius=50, start_angle=0, end_angle=180
)

# 计算延伸线
extended_x, extended_y, extended_z = calculator.extend_arc_to_cone_wall(
    x_arc, y_arc, z_arc
)

# 计算体积
volume = calculator.calculate_volume_between_arc_and_cone(
    height=800, arc_radius=50, start_angle=0, end_angle=180
)
```

### 与Fantasy项目集成

```python
from tools.cone_arc_volume import calculate_cone_arc_volume
from tools.environment import show_cone_from_yaml

# 获取Fantasy项目的圆锥数据
cone, details = show_cone_from_yaml(360000, 0, if_print=False)

# 使用项目数据计算体积
volume = calculate_cone_arc_volume(
    cone_angle=details['angle'],
    cone_height=cone.standard_height + 100000,
    arc_height=cone.standard_height + 50000,
    arc_radius=cone.standard_radius * 0.2,
    start_angle=0,
    end_angle=180,
    visualize=False
)
```

## 参数说明

### 圆锥参数
- `cone_angle`: 圆锥半顶角（度），决定圆锥的锥度
- `cone_height`: 圆锥高度，定义圆锥的总高度

### 圆弧参数
- `arc_height`: 圆弧所在的高度位置
- `arc_radius`: 圆弧的半径
- `arc_center_x`, `arc_center_y`: 圆弧中心的x、y坐标（默认为原点）
- `start_angle`, `end_angle`: 圆弧的起始和结束角度（度）

### 其他参数
- `visualize`: 是否显示3D可视化图形

## 计算原理

1. **圆弧生成**：根据给定参数在指定高度生成圆弧，过滤出圆锥内部的部分
2. **延伸计算**：对圆弧上的每个点，计算其竖直向下到圆锥壁的交点
3. **体积积分**：使用数值积分方法，将延伸区域分割成小的楔形体积元素进行累加

体积计算公式（楔形近似）：
```
V = Σ (1/2) * r² * Δθ * Δh
```
其中：
- r: 楔形的平均半径
- Δθ: 角度增量
- Δh: 高度增量

## 测试和验证

运行测试脚本验证功能：

```bash
python test_cone_arc_volume.py
```

运行示例脚本查看使用方法：

```bash
python cone_arc_volume_example.py
```

## 应用场景

1. **几何体积计算**：计算复杂几何体的体积
2. **工程设计**：圆锥形容器或结构的容积计算
3. **科学研究**：Fantasy项目中的圆锥世界体积分析
4. **教学演示**：几何学和积分学的可视化教学

## 注意事项

1. **参数合理性**：确保圆弧高度大于圆锥壁交点高度，否则体积为0
2. **圆弧范围**：圆弧半径过大时，可能完全超出圆锥范围
3. **计算精度**：使用数值方法，精度取决于采样点数量
4. **可视化环境**：3D可视化需要适当的图形环境支持

## 扩展功能

模块设计为可扩展的，可以轻松添加：
- 不同形状的截面（椭圆、多边形等）
- 更复杂的延伸模式
- 更精确的解析积分方法
- 批量计算和参数优化功能

## 版本信息

- 版本：1.0
- 作者：Augment Agent
- 创建日期：2024年
- 兼容性：Python 3.7+，需要numpy、matplotlib、sympy库
